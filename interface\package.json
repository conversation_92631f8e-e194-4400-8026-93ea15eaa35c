{"name": "qubitsim-interface", "version": "0.1.0", "type": "module", "private": true, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/material": "^5.13.0", "@mui/icons-material": "^5.11.16", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.11.1", "reactflow": "^11.7.0", "three": "^0.152.2", "@react-three/fiber": "^8.12.0", "@react-three/drei": "^9.65.3", "zustand": "^4.3.8", "axios": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-scripts": "5.0.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^4.9.5"}}