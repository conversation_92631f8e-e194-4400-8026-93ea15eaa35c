# 🌍 Guia de Internacionalização - QubitSim

## 📋 Introdução

Este guia fornece diretrizes e boas práticas para internacionalização (i18n) do QubitSim, garantindo que a aplicação seja acessível a usuários de diferentes idiomas e regiões.

## 🎯 Estrutura de Arquivos

### 1. Organização de Traduções
```
src/
├── i18n/
│   ├── locales/
│   │   ├── pt-BR/
│   │   │   ├── common.json
│   │   │   ├── algorithms.json
│   │   │   └── errors.json
│   │   ├── en/
│   │   │   ├── common.json
│   │   │   ├── algorithms.json
│   │   │   └── errors.json
│   │   └── es/
│   │       ├── common.json
│   │       ├── algorithms.json
│   │       └── errors.json
│   ├── config.js
│   └── index.js
```

### 2. Arquivos de Tradução
```json
// common.json
{
  "welcome": "Bem-vindo ao QubitSim",
  "navigation": {
    "home": "Início",
    "algorithms": "Algoritmos",
    "documentation": "Documentação"
  },
  "actions": {
    "run": "Executar",
    "stop": "Parar",
    "reset": "Reiniciar"
  }
}

// algorithms.json
{
  "deutschJozsa": {
    "title": "Algoritmo de Deutsch-Jozsa",
    "description": "Determina se uma função é constante ou balanceada",
    "parameters": {
      "numQubits": "Número de Qubits",
      "function": "Função"
    }
  }
}
```

## 🛠️ Implementação

### 1. Configuração
```javascript
// i18n/config.js
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

i18n
  .use(initReactI18next)
  .init({
    resources: {
      'pt-BR': {
        common: require('./locales/pt-BR/common.json'),
        algorithms: require('./locales/pt-BR/algorithms.json')
      },
      'en': {
        common: require('./locales/en/common.json'),
        algorithms: require('./locales/en/algorithms.json')
      }
    },
    lng: 'pt-BR',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false
    }
  });

export default i18n;
```

### 2. Uso em Componentes
```javascript
// Componente com traduções
import { useTranslation } from 'react-i18next';

const QuantumCircuit = () => {
  const { t } = useTranslation(['common', 'algorithms']);

  return (
    <div>
      <h1>{t('algorithms:deutschJozsa.title')}</h1>
      <p>{t('algorithms:deutschJozsa.description')}</p>
      <button>{t('common:actions.run')}</button>
    </div>
  );
};
```

## 🌍 Localização

### 1. Formatação de Números
```javascript
// Formatação de números
const formatNumber = (number, locale) => {
  return new Intl.NumberFormat(locale, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(number);
};

// Uso
const result = formatNumber(3.14159, 'pt-BR'); // 3,14
```

### 2. Formatação de Datas
```javascript
// Formatação de datas
const formatDate = (date, locale) => {
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(date);
};

// Uso
const date = formatDate(new Date(), 'pt-BR'); // 15 de março de 2024
```

### 3. Formatação de Moedas
```javascript
// Formatação de moedas
const formatCurrency = (amount, locale, currency) => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency
  }).format(amount);
};

// Uso
const price = formatCurrency(99.99, 'pt-BR', 'BRL'); // R$ 99,99
```

## 🎯 Componentes Internacionalizados

### 1. Componente de Seleção de Idioma
```javascript
const LanguageSelector = () => {
  const { i18n } = useTranslation();

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
  };

  return (
    <select
      value={i18n.language}
      onChange={(e) => changeLanguage(e.target.value)}
    >
      <option value="pt-BR">Português</option>
      <option value="en">English</option>
      <option value="es">Español</option>
    </select>
  );
};
```

### 2. Componente de Formulário
```javascript
const QuantumForm = () => {
  const { t } = useTranslation(['common', 'algorithms']);

  return (
    <form>
      <label>
        {t('algorithms:deutschJozsa.parameters.numQubits')}
        <input type="number" min="1" max="10" />
      </label>
      <button type="submit">
        {t('common:actions.run')}
      </button>
    </form>
  );
};
```

## 📝 Boas Práticas

### 1. Organização de Chaves
```javascript
// Use namespaces para organizar traduções
{
  "namespace": {
    "section": {
      "key": "valor"
    }
  }
}

// Exemplo
{
  "algorithms": {
    "deutschJozsa": {
      "title": "Algoritmo de Deutsch-Jozsa"
    }
  }
}
```

### 2. Interpolação
```javascript
// Use interpolação para valores dinâmicos
{
  "welcome": "Bem-vindo, {{name}}!"
}

// Uso
t('welcome', { name: 'João' });
```

### 3. Pluralização
```javascript
// Use pluralização para diferentes quantidades
{
  "qubits": {
    "one": "1 qubit",
    "other": "{{count}} qubits"
  }
}

// Uso
t('qubits', { count: 2 }); // "2 qubits"
```

## 🧪 Testes

### 1. Testes de Tradução
```javascript
// Teste de traduções
describe('Translations', () => {
  test('deve traduzir corretamente', () => {
    const { t } = useTranslation();
    expect(t('welcome')).toBe('Bem-vindo ao QubitSim');
  });

  test('deve lidar com interpolação', () => {
    const { t } = useTranslation();
    expect(t('hello', { name: 'João' }))
      .toBe('Olá, João!');
  });
});
```

### 2. Testes de Formatação
```javascript
// Teste de formatação
describe('Formatting', () => {
  test('deve formatar números corretamente', () => {
    expect(formatNumber(3.14159, 'pt-BR'))
      .toBe('3,14');
  });

  test('deve formatar datas corretamente', () => {
    const date = new Date('2024-03-15');
    expect(formatDate(date, 'pt-BR'))
      .toBe('15 de março de 2024');
  });
});
```

## 📚 Recursos Adicionais

### Links Úteis
- [i18next Documentation](https://www.i18next.com/)
- [React-i18next](https://react.i18next.com/)
- [ICU Message Format](http://userguide.icu-project.org/formatparse/messages)
- [Intl API](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl)

### Ferramentas
- i18next
- react-i18next
- i18next-browser-languagedetector
- i18next-http-backend 