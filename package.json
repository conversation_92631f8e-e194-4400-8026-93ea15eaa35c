{"name": "qubitsim", "version": "0.1.0", "description": "Uma biblioteca JavaScript para simulação de computação quântica e criptografia quântica", "main": "src/index.js", "type": "module", "scripts": {"test": "jest", "test:coverage": "jest --coverage", "lint": "eslint src/", "docs": "jsdoc -c jsdoc.json", "build": "babel src -d dist"}, "keywords": ["quantum", "computing", "cryptography", "simulation", "qubit", "quantum-gates"], "author": "<PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/maikonweber/qubitsim.git"}, "bugs": {"url": "https://github.com/maikonweber/qubitsim/issues"}, "homepage": "https://github.com/maikonweber/qubitsim", "dependencies": {"complex.js": "^2.1.1", "mathjs": "^11.8.0"}, "devDependencies": {"@babel/cli": "^7.21.5", "@babel/core": "^7.21.8", "@babel/preset-env": "^7.21.5", "eslint": "^8.40.0", "jest": "^29.5.0", "jsdoc": "^4.0.2"}}